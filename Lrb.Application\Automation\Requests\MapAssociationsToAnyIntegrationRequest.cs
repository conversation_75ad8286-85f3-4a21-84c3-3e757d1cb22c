﻿using Lrb.Application.Agency.Web;
using Lrb.Application.Campaigns.Spec;
using Lrb.Application.Integration.Web;
using Lrb.Application.Integration.Web.Specs;
using Lrb.Application.Lead.Web;
using Lrb.Application.Project.Web.Specs;
using Lrb.Application.ZonewiseLocation.Web.Specs;
using Lrb.Domain.Entities.Integration;

namespace Lrb.Application.Automation.Requests
{
    public class MapAssociationsToAnyIntegrationRequest : IRequest<Response<bool>>
    {
        public Guid Id { get; set; }
        public Guid? ProjectId { get; set; }
        public Guid? LocationId { get; set; }
        public LeadSource Source { get; set; }
        public string? CountryCode { get; set; }
        public string? Camapign { get; set; }
        public string? ChannelPartner { get; set; }
        public string? Agency { get; set; }
        public string? Property { get; set; }


    }
    public class MapAssociationsToAnyIntegrationRequestHandler : IRequestHandler<MapAssociationsToAnyIntegrationRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<FacebookAdsInfo> _fbAdsRepo;
        private readonly IRepositoryWithEvents<FacebookLeadGenForm> _fbFormRepo;
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationAccRepo;
        private readonly IRepositoryWithEvents<IntegrationAssignment> _integrationAssignmentRepo;
        private readonly IRepositoryWithEvents<Location> _locationRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.Property> _propertyRepo;
        private readonly IRepositoryWithEvents<Campaign> _campaignRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.ChannelPartner> _channelPartenerRepo;
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.Agency> _agencyRepo;

        public MapAssociationsToAnyIntegrationRequestHandler(
            IRepositoryWithEvents<FacebookAdsInfo> fbAdsRepo,
            IRepositoryWithEvents<FacebookLeadGenForm> fbFormRepo,
            IRepositoryWithEvents<IntegrationAccountInfo> integrationAccRepo,
            IRepositoryWithEvents<IntegrationAssignment> integrationAssignmentRepo,
            IRepositoryWithEvents<Location> locationRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.Project> projectRepo,
            IRepositoryWithEvents<Domain.Entities.Property> propertyRepo,
            IRepositoryWithEvents<Campaign> campaignRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.ChannelPartner> channelPartenerRepo,
            IRepositoryWithEvents<Lrb.Domain.Entities.Agency> agencyRepo)
        {
            _fbAdsRepo = fbAdsRepo;
            _fbFormRepo = fbFormRepo;
            _integrationAccRepo = integrationAccRepo;
            _integrationAssignmentRepo = integrationAssignmentRepo;
            _locationRepo = locationRepo;
            _projectRepo = projectRepo;
            _propertyRepo = propertyRepo;
            _campaignRepo = campaignRepo;
            _channelPartenerRepo = channelPartenerRepo;
            _agencyRepo = agencyRepo;
        }

        public async Task<Response<bool>> Handle(MapAssociationsToAnyIntegrationRequest request, CancellationToken cancellationToken)
        {
            var location = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(request.LocationId ?? Guid.Empty), cancellationToken);
            var project = await _projectRepo.FirstOrDefaultAsync(new GetProjectByIdSpecs(request.ProjectId ?? Guid.Empty), cancellationToken);
            Lrb.Domain.Entities.Property? property = null;
            Lrb.Domain.Entities.Agency? agency = null;
            Lrb.Domain.Entities.ChannelPartner? channelpartner = null;
            Lrb.Domain.Entities.Campaign? campaign = null;

            if (request.Property != null)
            {
                property = await _propertyRepo.FirstOrDefaultAsync(new GetPropertyByTitleSpec(request.Property), cancellationToken);
            }
            if (request.ChannelPartner != null)
            {
                channelpartner = await _channelPartenerRepo.FirstOrDefaultAsync(new GetChannelPartnerByNameSpecs(request.ChannelPartner), cancellationToken);
            }
            if (request.Camapign != null)
            {
                campaign = await _campaignRepo.FirstOrDefaultAsync(new GetCampaignByNameSpec(request.Camapign), cancellationToken);
            }
            if (request.Agency != null)
            {
                agency = await _agencyRepo.FirstOrDefaultAsync(new GetAgencyByNameSpec(request.Agency), cancellationToken);
            }

            var assignment = new IntegrationAssignment()
            {
                Location = location,
                Project = project,
                Agency = agency,
                Campaign = campaign,
                Property = property,
                ChannelPartner = channelpartner,

            };
            var integrationAcc = await GetIntegrationAccountInfoAsync(request.Source, request.Id);
            if (integrationAcc == null)
            {
                var fbAd = await _fbAdsRepo.FirstOrDefaultAsync(new FacebookAdsInfoByIdSpec(request.Id), cancellationToken);
                if (fbAd == null)
                {
                    var fbForm = await _fbFormRepo.FirstOrDefaultAsync(new FacebookFormByIdSpec(request.Id), cancellationToken);

                    if (!string.IsNullOrWhiteSpace(request?.CountryCode) && fbForm != null)
                    {
                        fbForm.CountryCode = request.CountryCode;
                    }
                    if (agency != null && fbForm!= null)
                    {
                        fbForm.AgencyName= agency.Name;
                    }
                    else
                    {
                        fbForm.AgencyName = string.Empty;
                    }

                    if (fbForm == null)
                    {
                        return new("Please provide a valid Id.");
                    }
                    else if (fbForm.Assignment != null)
                    {
                        fbForm.Assignment.Location = location;
                        fbForm.Assignment.Project = project;
                        fbForm.Assignment.Property = property;
                        fbForm.Assignment.Agency = agency;
                        fbForm.Assignment.ChannelPartner = channelpartner;
                        fbForm.Assignment.Campaign = campaign;

                    }
                    else
                    {
                        await _integrationAssignmentRepo.AddAsync(assignment, cancellationToken);
                        fbForm.Assignment = assignment;
                    }
                    await _fbFormRepo.UpdateAsync(fbForm, cancellationToken);
                    return new(true, "Account updated successfully.");
                }
                else
                {
                    if (!string.IsNullOrWhiteSpace(request.CountryCode))
                    {
                        fbAd.CountryCode = request.CountryCode;
                    }
                    if (agency != null && fbAd != null)
                    {
                        fbAd.AgencyName = agency.Name;
                    }
                    else
                    {
                        fbAd.AgencyName = string.Empty;
                    }
                    if (fbAd.Assignment != null)
                    {
                        fbAd.Assignment.Location = location;
                        fbAd.Assignment.Project = project;
                        fbAd.Assignment.Property = property;
                        fbAd.Assignment.Agency = agency;
                        fbAd.Assignment.ChannelPartner = channelpartner;
                        fbAd.Assignment.Campaign = campaign;
                    }
                    else
                    {
                        await _integrationAssignmentRepo.AddAsync(assignment, cancellationToken);
                        fbAd.Assignment = assignment;
                    }
                    await _fbAdsRepo.UpdateAsync(fbAd, cancellationToken);
                    return new(true, "Account updated successfully.");
                }
            }
            else
            {
                if (!string.IsNullOrWhiteSpace(request.CountryCode))
                {
                    integrationAcc.CountryCode = request.CountryCode;
                }
                if (agency != null)
                {
                    integrationAcc.AgencyName = agency?.Name;
                }
                else
                {
                    integrationAcc.AgencyName = string.Empty;
                }
                if (integrationAcc.Assignment != null)
                {
                    integrationAcc.Assignment.Location = location;
                    integrationAcc.Assignment.Project = project;
                    integrationAcc.Assignment.Property = property;
                    integrationAcc.Assignment.Agency = agency;
                    integrationAcc.Assignment.ChannelPartner = channelpartner;
                    integrationAcc.Assignment.Campaign = campaign;

                }
                else
                {
                    await _integrationAssignmentRepo.AddAsync(assignment, cancellationToken);
                    integrationAcc.Assignment = assignment;
                }
                await _integrationAccRepo.UpdateAsync(integrationAcc, cancellationToken);
                return new(true, "Account updated successfully.");
            }
        }
        private async Task<IntegrationAccountInfo?> GetIntegrationAccountInfoAsync(LeadSource source, Guid id)
        {
            IntegrationAccountInfo? integrationAccountInfo = null;
            switch (source)
            {
                case LeadSource.Facebook:
                    integrationAccountInfo = await _integrationAccRepo.FirstOrDefaultAsync(new IntegrationAccInfoByFacebookIdOrIdSpec(id), CancellationToken.None);
                    break;
                case LeadSource.Gmail:
                    integrationAccountInfo = await _integrationAccRepo.FirstOrDefaultAsync(new IntegrationAccByGmailIdOrIdSpec(id), CancellationToken.None);
                    break;
                case LeadSource.GoogleAds:
                    integrationAccountInfo = await _integrationAccRepo.FirstOrDefaultAsync(new IntegrationAccInfoByGoogleAdIdOrId(id), CancellationToken.None);
                    break;
                default:
                    integrationAccountInfo = await _integrationAccRepo.FirstOrDefaultAsync(new IntegrationAccountByIdSpec(id), CancellationToken.None);
                    break;
            }
            return integrationAccountInfo;
        }
    }
}
