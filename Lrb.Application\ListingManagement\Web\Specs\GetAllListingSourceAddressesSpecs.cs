﻿using Ardalis.Specification;
using Lrb.Application.ListingManagement.Web.Requests;

namespace Lrb.Application.ListingManagement.Web.Specs
{
    public class GetAllListingSourceAddressesSpecs : EntitiesByPaginationFilterSpec<ListingSourceAddress>
    {
        public GetAllListingSourceAddressesSpecs(GetAllListingSourceAddressesRequest filter) : base(filter) 
        {
            Query.Where(i => !i.IsDeleted)
                .Include(i => i.ListingSource);

            if(filter.ListingSourceId != null)
            {
                Query.Where(i => i.ListingSource.Id == filter.ListingSourceId);
            }
        }
    }

    public class GetAllListingSourceAddressesCountSpecs : Specification<ListingSourceAddress>
    {
        public GetAllListingSourceAddressesCountSpecs()
        {
            Query.Where(i => !i.IsDeleted);
        }
    }


    public class GetListingSourceAddressByIdSpecs : Specification<ListingSourceAddress>
    {
        public GetListingSourceAddressByIdSpecs(Guid id)
        {
            Query.Where(i => !i.IsDeleted && i.Id == id);
        }
    }

    public class GetListedAddressFromLrbTable : Specification<ListingSourceAddress>
    {
        public GetListedAddressFromLrbTable(string tower, string subCommunity, string community, string city, Guid sourceId)
        {
            if(!string.IsNullOrEmpty(tower))
            {
                Query.Where(i => i.TowerName == tower && i.SubCommunity == subCommunity && i.Community == community && i.City == city && i.ListingSourceId == sourceId);
            }
            else
            {
                Query.Where(i => i.SubCommunity == subCommunity && i.Community == community && i.City == city && i.ListingSourceId == sourceId);
            }
        }
    }

    public class CheckDuplicateListingSourceAddressesSpecs : Specification<ListingSourceAddress>
    {
        public CheckDuplicateListingSourceAddressesSpecs(List<(string TowerName, string SubCommunity, string Community, string City, Guid ListingSourceId)> addressKeys)
        {
            if (addressKeys?.Any() ?? false)
            {
                Query.Where(i => !i.IsDeleted);

                // Use a more efficient approach with Contains for bulk checking
                var towerCommunityKeys = addressKeys.Where(a => !string.IsNullOrEmpty(a.TowerName))
                    .Select(a => new { a.TowerName, a.SubCommunity, a.Community, a.City, a.ListingSourceId }).ToList();

                var nonTowerKeys = addressKeys.Where(a => string.IsNullOrEmpty(a.TowerName))
                    .Select(a => new { a.SubCommunity, a.Community, a.City, a.ListingSourceId }).ToList();

                if (towerCommunityKeys.Any() && nonTowerKeys.Any())
                {
                    Query.Where(i =>
                        towerCommunityKeys.Any(k => i.TowerName == k.TowerName &&
                                                   i.SubCommunity == k.SubCommunity &&
                                                   i.Community == k.Community &&
                                                   i.City == k.City &&
                                                   i.ListingSourceId == k.ListingSourceId) ||
                        nonTowerKeys.Any(k => i.SubCommunity == k.SubCommunity &&
                                             i.Community == k.Community &&
                                             i.City == k.City &&
                                             i.ListingSourceId == k.ListingSourceId));
                }
                else if (towerCommunityKeys.Any())
                {
                    Query.Where(i => towerCommunityKeys.Any(k => i.TowerName == k.TowerName &&
                                                               i.SubCommunity == k.SubCommunity &&
                                                               i.Community == k.Community &&
                                                               i.City == k.City &&
                                                               i.ListingSourceId == k.ListingSourceId));
                }
                else if (nonTowerKeys.Any())
                {
                    Query.Where(i => nonTowerKeys.Any(k => i.SubCommunity == k.SubCommunity &&
                                                         i.Community == k.Community &&
                                                         i.City == k.City &&
                                                         i.ListingSourceId == k.ListingSourceId));
                }
            }
        }
    }

    public class GetAllListingSourceAddressesForBulkUploadSpecs : Specification<ListingSourceAddress>
    {
        public GetAllListingSourceAddressesForBulkUploadSpecs()
        {
            Query.Where(i => !i.IsDeleted);
        }
    }

    public class GetListingSourceAddressBySourceIdSpecs : Specification<ListingSourceAddress>
    {
        public GetListingSourceAddressBySourceIdSpecs(Guid sourceId)
        {
            Query.Where(i => !i.IsDeleted && i.ListingSourceId == sourceId);
        }
    }
}
