﻿using ExcelUpload;
using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.ListingManagement.Web.Dtos;
using Lrb.Application.ListingManagement.Web.Helper;
using Lrb.Application.ListingManagement.Web.Specs;
using Lrb.Application.Property.Web;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Enums;
using Newtonsoft.Json;
using System.Collections.Concurrent;
using System.Data;

namespace ExcelUpload
{
    public partial class FunctionEntryPoint
    {
        public async Task ListingSourceAddressHandler(InputPayload input)
        {
            CancellationToken cancellationToken = CancellationToken.None;
            var tracker = await _listingSourceAddresstrackerRepo.FirstOrDefaultAsync(new BulkListingSourceAddressTrackerSpecs(input.TrackerId));
            try
            {
                if (tracker != null)
                {
                    tracker.MappedColumnsData = tracker.MappedColumnsData?.ToDictionary(i => i.Key, j => j.Value?.Trim() ?? string.Empty);
                    tracker.Status = UploadStatus.Started;
                    tracker.LastModifiedBy = input.CurrentUserId;
                    tracker.CreatedBy = input.CurrentUserId;

                    await _listingSourceAddresstrackerRepo.UpdateAsync(tracker);
                    Console.WriteLine($"FunctionEntryPoint() -> ListingSourceAddressUploadTracker Updated Status: {tracker.Status} \n {JsonConvert.SerializeObject(tracker)}");

                    #region Convert to Datatable

                    Stream fileStream = await _blobStorageService.GetObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", tracker.S3BucketKey);
                    DataTable dataTable = new();
                    if (tracker.S3BucketKey.Split('.').LastOrDefault() == "csv")
                    {
                        using MemoryStream memoryStream = new();
                        fileStream.CopyTo(memoryStream);
                        dataTable = CSVHelper.CSVToDataTable(memoryStream);
                    }
                    else
                    {
                        dataTable = EPPlusExcelHelper.ConvertExcelToDataTable(fileStream, tracker.SheetName);
                    }

                    List<InvalidProspect> invalids = new();
                    int totalRows = dataTable.Rows.Count;
                    for (int i = totalRows - 1; i >= 0; i--)
                    {
                        var row = dataTable.Rows[i];
                        if (row.ItemArray.All(i => string.IsNullOrEmpty(i.ToString())))
                        {
                            row.Delete();
                        }
                    }
                    if (dataTable.Rows.Count <= 0)
                    {
                        throw new Exception("Excel sheet is empty. Please fill some data in the excel sheet template.");
                    }
                    totalRows = dataTable.Rows.Count;
                    Console.WriteLine($"handler() -> Total Rows in the Excel: {dataTable.Rows.Count}");

                    #endregion

                    #region Fetch All requires data
                    var listingSource = await _customListingSource.FirstOrDefaultAsync(new GetAllListingSourceByIds(tracker.ListingSourceId ?? Guid.Empty));
                    #endregion

                    var listingSourceAddresses = dataTable.ConvertToListingSourceAddress(tracker.MappedColumnsData, listingSource);
                    listingSourceAddresses.ForEach(i => i.SetListingSourceAddress(tracker.MappedColumnsData, input.CurrentUserId));

                    // Filter out duplicate addresses
                    var uniqueListingSourceAddresses = new List<Lrb.Domain.Entities.ListingSourceAddress>();
                    int duplicateCount = 0;

                    foreach (var address in listingSourceAddresses)
                    {
                        // Check if this address already exists in the database
                        var existingAddress = await _listingSourceAddressRepo.FirstOrDefaultAsync(
                            new GetListedAddressFromLrbTable(
                                address.TowerName ?? string.Empty,
                                address.SubCommunity ?? string.Empty,
                                address.Community ?? string.Empty,
                                address.City ?? string.Empty,
                                address.ListingSourceId ?? Guid.Empty
                            ), cancellationToken);

                        if (existingAddress == null)
                        {
                            // Address doesn't exist, add to unique list
                            uniqueListingSourceAddresses.Add(address);
                        }
                        else
                        {
                            // Address already exists, increment duplicate count
                            duplicateCount++;
                        }
                    }

                    // Update the list to only include unique addresses
                    listingSourceAddresses = uniqueListingSourceAddresses;

                    tracker.Status = UploadStatus.InProgress;
                    tracker.TotalCount = totalRows;
                    tracker.DuplicateCount = duplicateCount;
                    //tracker.InvalidCount = invalidChannelPartners.Count;
                    await _listingSourceAddresstrackerRepo.UpdateAsync(tracker);

                    Console.WriteLine($"handler() -> Total Rows: {totalRows}, Unique Addresses: {listingSourceAddresses.Count}, Duplicates Found: {duplicateCount}");

                    if (listingSourceAddresses.Count > 0)
                    {
                        int listingSourceAddressChunks = listingSourceAddresses.Count > 5000 ? 5000 : listingSourceAddresses.Count;
                        var chunks = listingSourceAddresses.Chunk(listingSourceAddressChunks).Select(i => new ConcurrentBag<Lrb.Domain.Entities.ListingSourceAddress>(i));
                        var tenantInfo = await _tenantIndependentRepo.GetTenantInfoAsync(input.TenantId);
                        var chunkIndex = 1;
                        foreach (var chunk in chunks.ToList())
                        {
                            var backgroundDto = new BulkAddressUploadBackgroundDto()
                            {
                                TrackerId = tracker?.Id ?? Guid.Empty,
                                TenantInfoDto = tenantInfo,
                                CancellationToken = CancellationToken.None,
                                ListingSourceAddresses = new(chunk),
                                CurrentUserId = input.CurrentUserId,
                            };
                            if (chunkIndex == chunks.Count())
                            {
                                backgroundDto.IsLastChunk = true;
                            }
                            await ExecuteDBOperationsAsync(backgroundDto);
                            chunkIndex++;
                        }
                    }
                    else if (duplicateCount > 0)
                    {
                        // All records were duplicates, mark as completed
                        tracker.Status = UploadStatus.Completed;
                        tracker.Message = $"Upload completed. All {duplicateCount} records were duplicates and were skipped.";
                        await _listingSourceAddresstrackerRepo.UpdateAsync(tracker);
                        Console.WriteLine($"handler() -> All records were duplicates. Upload marked as completed.");
                    }
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "ListingSourceAddressHandler -> ListingSourceHandler()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                Console.WriteLine($"handler() -> Exception:  {JsonConvert.SerializeObject(ex, settings: new() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                throw;
            }
        }

        public async Task ExecuteDBOperationsAsync(BulkAddressUploadBackgroundDto dto)
        {
            ListingSourceAddress listingAddress = new();
            var tracker = await _listingSourceAddresstrackerRepo.FirstOrDefaultAsync(new BulkListingSourceAddressTrackerSpecs(dto.TrackerId));
            try
            {
                await _listingSourceAddressRepo.AddRangeAsync(dto.ListingSourceAddresses);
                if (tracker != null)
                {
                    tracker.TotalUploadedCount += dto.ListingSourceAddresses.Count;
                    if (dto.IsLastChunk)
                    {
                        tracker.Status = UploadStatus.Completed;
                    }
                    tracker.LastModifiedBy = dto.CurrentUserId;
                    tracker.CreatedBy = dto.CurrentUserId;
                    await _listingSourceAddresstrackerRepo.UpdateAsync(tracker);
                }
            }
            catch (Exception e)
            {
                Console.WriteLine($"ExecuteDBOperationsAsync() -> Exception: {JsonConvert.SerializeObject(e)}");
                if (tracker.TotalUploadedCount == tracker.InvalidCount)
                {
                    tracker.Status = UploadStatus.Completed;
                }
                else
                {
                    tracker.Status = UploadStatus.Failed;
                    tracker.Message = e?.InnerException?.Message ?? e?.Message;
                }
                tracker.LastModifiedBy = dto.CurrentUserId;
                tracker.CreatedBy = dto.CurrentUserId;
                await _listingSourceAddresstrackerRepo.UpdateAsync(tracker);
            }
        }
    }
}
